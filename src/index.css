@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Mobile App Design System */
    --background: 240 15% 97%;
    --foreground: 220 20% 15%;

    --card: 0 0% 100%;
    --card-foreground: 220 20% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 220 20% 15%;

    /* Primary - E-commerce Orange/Red */
    --primary: 14 90% 53%;
    --primary-foreground: 0 0% 100%;
    --primary-light: 14 90% 65%;

    /* Secondary - Soft Blue */
    --secondary: 215 80% 95%;
    --secondary-foreground: 215 25% 35%;

    --muted: 220 15% 95%;
    --muted-foreground: 220 10% 50%;

    --accent: 280 60% 85%;
    --accent-foreground: 280 30% 25%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 100%;

    --border: 220 15% 90%;
    --input: 220 15% 95%;
    --ring: 14 90% 53%;

    /* Mobile specific */
    --mobile-header: 0 0% 100%;
    --flash-sale: 3 92% 55%;
    --flash-sale-bg: 3 100% 97%;
    --category-icon: 210 20% 98%;
    --price-text: 14 90% 53%;
    --discount-badge: 0 100% 65%;

    /* Gradients */
    --gradient-flash: linear-gradient(135deg, hsl(3 92% 55%), hsl(14 90% 53%));
    --gradient-card: linear-gradient(180deg, hsl(0 0% 100%), hsl(220 15% 98%));

    --radius: 12px;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }

  /* Hide scrollbar for Chrome, Safari and Opera */
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  .scrollbar-hide {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }

  /* Smooth scrolling */
  .scroll-smooth {
    scroll-behavior: smooth;
  }

  /* Line clamp utilities */
  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
}
