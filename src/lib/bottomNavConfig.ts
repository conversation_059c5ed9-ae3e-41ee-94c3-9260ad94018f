// Configuration utility for BottomNavigation component

export interface NavItem {
  id?: string;
  icon: string;
  label: string;
  active?: boolean;
  href?: string;
  type?: string;
  iconActive?: string;
  typeActive?: string;
}

export interface MenuBottomConfig {
  visible: boolean;
  layouts: NavItem[];
  settings: {
    margin?: string;
    padding?: string;
    background?: string;
    border?: string;
    borderRadius?: string;
    boxShadow?: string;
    cardStyle?: {
      inactiveColor?: string;
      activeColor?: string;
      activeBackground?: string;
      sizeIcon?: number;
      visibleLabel?: boolean;
      fontSizeLabel?: number;
    };
  };
}

// Example configuration that matches your provided data
export const defaultMenuBottomConfig: { [key: string]: MenuBottomConfig } = {
  Menu1: {
    visible: true,
    layouts: [
      {
        label: "Trang chủ",
        icon: "Home",
        type: "iconly",
        iconActive: "Home",
        typeActive: "iconly",
        href: "/home"
      },
      {
        label: "Danh mục",
        icon: "Category",
        type: "iconly",
        iconActive: "Category",
        typeActive: "iconly",
        href: "/category"
      },
      {
        label: "Tin tức",
        icon: "Paper",
        type: "iconly",
        iconActive: "Paper",
        typeActive: "iconly",
        href: "/news"
      },
      {
        label: "Cá nhân",
        icon: "User",
        type: "iconly",
        iconActive: "User",
        typeActive: "iconly",
        href: "/profile"
      }
    ],
    settings: {
      margin: "0px 0px 0px 0px",
      padding: "0px 0px 0px 0px",
      background: "rgba(255, 255, 255, 1)",
      border: "0px black solid",
      borderRadius: "0px 0px 0px 0px",
      boxShadow: "",
      cardStyle: {
        inactiveColor: "rgba(40, 40, 55, 1)",
        activeColor: "rgba(4, 117, 231, 1)",
        activeBackground: "rgba(4, 117, 231, 0.25)",
        sizeIcon: 24,
        visibleLabel: true,
        fontSizeLabel: 12
      }
    }
  }
};

// Helper function to create a menu configuration
export const createMenuConfig = (
  items: Omit<NavItem, 'type' | 'typeActive'>[],
  customSettings?: Partial<MenuBottomConfig['settings']>
): MenuBottomConfig => {
  return {
    visible: true,
    layouts: items.map(item => ({
      ...item,
      type: "iconly",
      typeActive: "iconly"
    })),
    settings: {
      margin: "0px 0px 0px 0px",
      padding: "0px 0px 0px 0px",
      background: "rgba(255, 255, 255, 1)",
      border: "0px black solid",
      borderRadius: "0px 0px 0px 0px",
      boxShadow: "",
      cardStyle: {
        inactiveColor: "rgba(40, 40, 55, 1)",
        activeColor: "rgba(4, 117, 231, 1)",
        activeBackground: "rgba(4, 117, 231, 0.25)",
        sizeIcon: 24,
        visibleLabel: true,
        fontSizeLabel: 12
      },
      ...customSettings
    }
  };
};

// Helper function to convert old format to new format
export const convertLegacyNavItems = (
  legacyItems: Array<{ id: string; icon: string; label: string; active?: boolean }>
): NavItem[] => {
  return legacyItems.map(item => ({
    id: item.id,
    icon: item.icon,
    label: item.label,
    active: item.active,
    type: "emoji" // Legacy items use emoji
  }));
};
