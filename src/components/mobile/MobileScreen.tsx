import React from "react";
import { cn } from "@/lib/utils";
import { DeviceModel } from "./DeviceSelector";

interface MobileScreenProps {
  children: React.ReactNode;
  className?: string;
  deviceModel?: DeviceModel;
}

export const MobileScreen: React.FC<MobileScreenProps> = ({
  children,
  className,
  deviceModel,
}) => {
  const device = deviceModel || {
    width: 375,
    height: 800,
    borderRadius: "rounded-3xl",
    statusBarHeight: 24,
  };

  const renderStatusBar = () => {
    const StatusBarContent = () => (
      <div className="w-full flex justify-between items-center px-3">
        {/* Time */}
        <div className="text-black text-sm font-semibold">10:33</div>

        {/* Right side indicators */}
        <div className="flex items-center gap-1">
          {/* Signal bars */}
          <svg width="17" height="12" viewBox="0 0 68 48" fill="none">
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M10.2001 25.96H13.5601C15.4158 25.96 16.9201 27.4643 16.9201 29.32V37.72C16.9201 39.5757 15.4158 41.08 13.5601 41.08H10.2001C8.34441 41.08 6.84009 39.5757 6.84009 37.72V29.32C6.84009 27.4643 8.34441 25.96 10.2001 25.96Z"
              fill="black"
            />
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M25.32 20.92H28.68C30.5356 20.92 32.04 22.4243 32.04 24.28V37.72C32.04 39.5757 30.5356 41.08 28.68 41.08H25.32C23.4643 41.08 21.96 39.5757 21.96 37.72V24.28C21.96 22.4243 23.4643 20.92 25.32 20.92Z"
              fill="black"
            />
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M40.4401 14.2H43.8001C45.6558 14.2 47.1601 15.7043 47.1601 17.56V37.72C47.1601 39.5757 45.6558 41.08 43.8001 41.08H40.4401C38.5844 41.08 37.0801 39.5757 37.0801 37.72V17.56C37.0801 15.7043 38.5844 14.2 40.4401 14.2Z"
              fill="black"
            />
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M55.56 7.48H58.92C60.7756 7.48 62.28 8.98433 62.28 10.84V37.72C62.28 39.5757 60.7756 41.08 58.92 41.08H55.56C53.7043 41.08 52.2 39.5757 52.2 37.72V10.84C52.2 8.98433 53.7043 7.48 55.56 7.48Z"
              fill="#3C3C43"
              fillOpacity="0.18"
            />
          </svg>

          {/* WiFi */}
          <svg width="15" height="12" viewBox="0 0 55 48" fill="none">
            <path
              d="M28.0858 30.797C30.2346 30.797 32.3224 31.3616 34.1621 32.4382L34.9074 32.8744C35.4725 33.2052 35.5719 33.9791 35.1086 34.4412L28.7445 40.7876C28.3536 41.1774 27.7198 41.1774 27.3289 40.7876L21.0057 34.482C20.5444 34.022 20.6405 33.2519 21.2008 32.9188L21.9379 32.4805C23.7944 31.3767 25.9088 30.797 28.0858 30.797Z"
              fill="black"
            />
            <path
              d="M28.0857 19.1387C33.4309 19.1387 38.5631 20.9119 42.7439 24.1957L43.3348 24.6598C43.8058 25.0298 43.8472 25.727 43.4232 26.1498L39.6238 29.9387C39.2724 30.2891 38.7163 30.3294 38.3178 30.0332L37.8547 29.689C35.031 27.5904 31.6261 26.4647 28.0857 26.4647C24.5236 26.4647 21.0988 27.6044 18.2658 29.7271L17.8021 30.0744C17.4036 30.373 16.8455 30.3337 16.4931 29.9822L12.695 26.1948C12.2719 25.7728 12.3121 25.0774 12.781 24.7067L13.3685 24.2423C17.5593 20.9292 22.7148 19.1387 28.0857 19.1387Z"
              fill="black"
            />
            <path
              d="M28.0861 7.48C36.569 7.48 44.6812 10.4723 51.1022 15.9571L51.6503 16.4253C52.0932 16.8036 52.1193 17.4779 51.7069 17.8892L47.9199 21.6657C47.553 22.0316 46.9665 22.0573 46.5689 21.7248L46.1 21.3327C41.0445 17.1055 34.708 14.806 28.0861 14.806C21.4415 14.806 15.0847 17.1213 10.0217 21.375L9.55257 21.7691C9.15506 22.1031 8.56737 22.078 8.19984 21.7115L4.4133 17.9355C4.00149 17.5248 4.02683 16.8516 4.46836 16.4729L5.01435 16.0046C11.4433 10.4902 19.578 7.48 28.0861 7.48Z"
              fill="#3C3C43"
              fillOpacity="0.3"
            />
          </svg>

          {/* Battery */}
          <svg width="24" height="12" viewBox="0 0 24 12" fill="none">
            <rect
              x="1"
              y="2"
              width="20"
              height="8"
              rx="2"
              stroke="black"
              strokeWidth="1"
              fill="none"
            />
            <rect x="2" y="3" width="16" height="6" rx="1" fill="#34C759" />
            <rect x="21" y="4" width="2" height="4" rx="1" fill="black" />
          </svg>
        </div>
      </div>
    );

    if (deviceModel?.dynamicIsland) {
      return (
        <div
          className="bg-white flex items-center justify-center relative"
          style={{ height: "44px" }}
        >
          <div className="absolute top-2 left-1/2 transform -translate-x-1/2 w-32 h-7 bg-black rounded-full"></div>
          <StatusBarContent />
        </div>
      );
    }

    if (deviceModel?.notch) {
      return (
        <div
          className="bg-white flex items-center justify-center relative"
          style={{ height: "44px" }}
        >
          <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-40 h-7 bg-black rounded-b-2xl"></div>
          <StatusBarContent />
        </div>
      );
    }

    return (
      <div
        className="bg-white flex items-center justify-center"
        style={{ height: "20px" }}
      >
        <StatusBarContent />
      </div>
    );
  };

  return (
    <div
      className={cn(
        "mx-auto bg-mobile-header",
        "border border-border overflow-hidden",
        "relative",
        device.borderRadius,
        className
      )}
      style={{
        width: `${device.width}px`,
        height: `${device.height}px`,
      }}
    >
      {/* Status Bar */}
      {renderStatusBar()}

      {/* Screen Content */}
      <div
        className="bg-background overflow-hidden"
        style={{
          height: `${device.height - device.statusBarHeight}px`,
        }}
      >
        {children}
      </div>
    </div>
  );
};
