import React from "react";
import { cn } from "@/lib/utils";

interface NewsItem {
  title: string;
  slug: string;
  description: string;
  summary: string;
  image: string;
  image_url: string;
  xid: string;
  x_news_category_id: string;
  status: string;
  tags: string;
  video?: string | null;
  product: any[];
}

interface NewsCardProps {
  news: NewsItem;
  className?: string;
  cardStyle?: any;
  onClick?: () => void;
}

export const NewsCard: React.FC<NewsCardProps> = ({
  news,
  className,
  cardStyle,
  onClick,
}) => {
  const imageUrl = news.image_url || news.image;
  
  return (
    <div
      className={cn(
        "bg-white rounded-lg overflow-hidden cursor-pointer transition-shadow duration-200 hover:shadow-md",
        className
      )}
      style={{
        height: cardStyle?.height ? `${cardStyle.height}px` : "auto",
        margin: cardStyle?.margin || "0px",
        padding: cardStyle?.padding || "6px",
        background: cardStyle?.background || "rgba(255,255,255,1)",
        border: cardStyle?.border || "1px solid #0000000d",
        borderRadius: cardStyle?.borderRadius || "6px",
        boxShadow: cardStyle?.boxShadow || "",
      }}
      onClick={onClick}
    >
      {/* News Image */}
      {imageUrl && (
        <div className="aspect-video bg-gray-100 rounded-md overflow-hidden mb-2">
          <img
            src={imageUrl}
            alt={news.title}
            className="w-full h-full object-cover"
          />
        </div>
      )}

      {/* News Content */}
      <div className="space-y-1">
        {/* News Title */}
        {cardStyle?.visibleNewsName !== false && (
          <h3 className="text-sm font-medium text-gray-900 line-clamp-2 leading-tight">
            {news.title}
          </h3>
        )}

        {/* News Summary */}
        <div
          className="text-xs text-gray-600 line-clamp-2"
          dangerouslySetInnerHTML={{
            __html: news.summary || news.description?.substring(0, 100) + "..." || "",
          }}
        />

        {/* News Time - if visible */}
        {cardStyle?.visibleNewsTime && (
          <div className="text-xs text-gray-400 mt-1">
            {/* You can add timestamp here if available in data */}
            Mới cập nhật
          </div>
        )}
      </div>
    </div>
  );
};

interface NewsListProps {
  newsList: NewsItem[];
  title?: string;
  titleStyle?: {
    color?: string;
    fontSize?: number;
  };
  cardStyle?: any;
  className?: string;
  onNewsClick?: (news: NewsItem) => void;
}

export const NewsList: React.FC<NewsListProps> = ({
  newsList,
  title = "Tin tức",
  titleStyle,
  cardStyle,
  className,
  onNewsClick,
}) => {
  if (!newsList || newsList.length === 0) {
    return (
      <div className={cn("p-4", className)}>
        {title && (
          <h2
            className="font-semibold mb-3"
            style={{
              color: titleStyle?.color || "rgba(0,0,0,1)",
              fontSize: titleStyle?.fontSize ? `${titleStyle.fontSize}px` : "14px",
            }}
          >
            {title}
          </h2>
        )}
        <div className="text-center text-gray-500 py-8">
          <div className="text-gray-400 mb-2">📰</div>
          <p className="text-sm">Chưa có tin tức nào</p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("", className)}>
      {title && (
        <h2
          className="font-semibold mb-3"
          style={{
            color: titleStyle?.color || "rgba(0,0,0,1)",
            fontSize: titleStyle?.fontSize ? `${titleStyle.fontSize}px` : "14px",
          }}
        >
          {title}
        </h2>
      )}
      
      <div className="grid grid-cols-2 gap-3">
        {newsList.map((news, index) => (
          <NewsCard
            key={news.xid || index}
            news={news}
            cardStyle={cardStyle}
            onClick={() => onNewsClick?.(news)}
          />
        ))}
      </div>
    </div>
  );
};
