import React from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination, Autoplay } from "swiper/modules";
import { cn } from "@/lib/utils";

// Import Swiper styles
import "swiper/css";
import "swiper/css/pagination";
import "./banner-swiper.css";

interface BannerItem {
  xid?: string;
  title?: string;
  subtitle?: string;
  image_url?: string;
  url?: string; // For existing data structure
  link_banner?: string; // For existing data structure
  backgroundColor?: string;
  textColor?: string;
}

interface BannerCardProps {
  title: string;
  subtitle?: string;
  image: string;
  backgroundColor?: string;
  textColor?: string;
  className?: string;
  cardStyle?: any;
}

interface BannerSwiperProps {
  banners: BannerItem[];
  settings?: any;
  autoplay?: boolean;
  autoplayDelay?: number;
  showPagination?: boolean;
  height?: string;
}

// Single Banner Card Component
export const BannerCard: React.FC<BannerCardProps> = ({
  title,
  subtitle,
  image,
  backgroundColor = "bg-gradient-to-r from-accent to-accent/80",
  textColor = "text-accent-foreground",
  className,
  cardStyle,
}) => {
  return (
    <div
      className={cn(
        "relative rounded-xl overflow-hidden h-24 flex items-center",
        backgroundColor,
        className
      )}
      style={{
        background: cardStyle?.background || "",
        border: cardStyle?.border || "",
        borderRadius: cardStyle?.borderRadius || "",
        padding: cardStyle?.padding || "",
        margin: cardStyle?.margin || "",
        boxShadow: cardStyle?.boxShadow || "",
      }}
    >
      <div className="flex-1 p-4 z-10">
        <h3 className={cn("font-bold text-lg leading-tight", textColor)}>
          {title}
        </h3>
        {subtitle && (
          <p className={cn("text-sm opacity-80", textColor)}>{subtitle}</p>
        )}
      </div>

      <div className="absolute right-0 top-0 h-full w-20 opacity-50">
        <img src={image} alt={title} className="w-full h-full object-cover" />
      </div>
    </div>
  );
};

// Banner Swiper Component
export const BannerSwiper: React.FC<BannerSwiperProps> = ({
  banners,
  settings,
  autoplay = true,
  autoplayDelay = 3000,
  showPagination = true,
  height = "164px",
}) => {
  // Use height from settings if available, otherwise use prop
  const bannerHeight = settings?.imageStyle?.height
    ? `${settings.imageStyle.height}px`
    : height;
  if (!banners || banners.length === 0) {
    return (
      <div
        className="bg-gray-100 rounded-xl flex items-center justify-center"
        style={{ height: bannerHeight }}
      >
        <p className="text-gray-500">Không có banner nào</p>
      </div>
    );
  }

  // If only one banner, show it without swiper
  if (banners.length === 1) {
    const banner = banners[0];
    const imageUrl = banner.image_url || banner.url;
    const title = banner.title || "Banner";
    const hasTextContent = banner.title || banner.subtitle;

    // If no text content, show as full image
    if (!hasTextContent && imageUrl) {
      return (
        <div
          className="relative rounded-xl overflow-hidden"
          style={{
            background: settings?.imageStyle?.background || "",
            border: settings?.imageStyle?.border || "",
            borderRadius: settings?.imageStyle?.borderRadius || "",
            padding: settings?.imageStyle?.padding || "",
            margin: settings?.imageStyle?.margin || "",
            boxShadow: settings?.imageStyle?.boxShadow || "",
            height: bannerHeight,
          }}
        >
          <img
            src={imageUrl}
            alt={title}
            className="w-full h-full object-cover"
          />
        </div>
      );
    }

    // Show with text content
    return (
      <BannerCard
        title={title}
        subtitle={banner.subtitle}
        image={imageUrl || ""}
        backgroundColor={banner.backgroundColor}
        textColor={banner.textColor}
        cardStyle={settings?.imageStyle}
      />
    );
  }

  return (
    <div className="relative">
      <Swiper
        modules={[Pagination, Autoplay]}
        spaceBetween={0}
        slidesPerView={1}
        pagination={
          showPagination
            ? {
                clickable: true,
                bulletClass: "swiper-pagination-bullet",
                bulletActiveClass: "swiper-pagination-bullet-active",
              }
            : false
        }
        autoplay={
          autoplay
            ? {
                delay: autoplayDelay,
                disableOnInteraction: false,
              }
            : false
        }
        loop={banners.length > 1}
        className="banner-swiper"
      >
        {banners.map((banner, index) => {
          // Handle both data structures
          const imageUrl = banner.image_url || banner.url;
          const title = banner.title || `Banner ${index + 1}`;
          const hasTextContent = banner.title || banner.subtitle;

          return (
            <SwiperSlide key={banner.xid || index}>
              <div
                className={cn(
                  "relative rounded-xl overflow-hidden",
                  hasTextContent ? "flex items-center" : "",
                  banner.backgroundColor ||
                    (hasTextContent
                      ? "bg-gradient-to-r from-blue-500 to-purple-600"
                      : "bg-gray-100")
                )}
                style={{
                  background:
                    settings?.imageStyle?.background ||
                    banner.backgroundColor ||
                    "",
                  border: settings?.imageStyle?.border || "",
                  borderRadius: settings?.imageStyle?.borderRadius || "",
                  padding: settings?.imageStyle?.padding || "",
                  margin: settings?.imageStyle?.margin || "",
                  boxShadow: settings?.imageStyle?.boxShadow || "",
                  height: bannerHeight,
                }}
              >
                {/* Text content - only show if we have title or subtitle */}
                {hasTextContent && (
                  <div className="flex-1 p-4 z-10">
                    <h3
                      className={cn(
                        "font-bold text-lg leading-tight",
                        banner.textColor || "text-white"
                      )}
                    >
                      {title}
                    </h3>
                    {banner.subtitle && (
                      <p
                        className={cn(
                          "text-sm opacity-80 mt-1",
                          banner.textColor || "text-white"
                        )}
                      >
                        {banner.subtitle}
                      </p>
                    )}
                  </div>
                )}

                {/* Image - full width if no text, partial if text exists */}
                {imageUrl && (
                  <div
                    className={cn(
                      hasTextContent
                        ? "absolute right-0 top-0 h-full w-24 opacity-70"
                        : "w-full h-full"
                    )}
                  >
                    <img
                      src={imageUrl}
                      alt={title}
                      className="w-full h-full object-cover"
                    />
                  </div>
                )}
              </div>
            </SwiperSlide>
          );
        })}
      </Swiper>

      {/* Custom pagination styles are handled via CSS classes */}
    </div>
  );
};
