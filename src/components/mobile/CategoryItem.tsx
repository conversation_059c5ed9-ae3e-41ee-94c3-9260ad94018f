import React from "react";
import { cn } from "@/lib/utils";

interface CategoryItemProps {
  title: string;
  image?: string;
  className?: string;
  onClick?: () => void;
  cardStyle?: any;
}

export const CategoryItem: React.FC<CategoryItemProps> = ({
  title,
  image,
  className,
  onClick,
  cardStyle,
}) => {
  return (
    <div
      className={"flex flex-col items-center cursor-pointer gap-2"}
      onClick={onClick}
    >
      {image ? (
        <img
          src={image}
          alt={title}
          style={{
            background: cardStyle?.iconStyle.background || "transparent",
            border: cardStyle?.iconStyle.border || "none",
            borderRadius: cardStyle?.iconStyle.borderRadius || "8px",
            padding: cardStyle?.iconStyle.padding || "8px",
            margin: cardStyle?.iconStyle.margin || "0px",
            boxShadow: cardStyle?.iconStyle.boxShadow || "",
            width: cardStyle?.iconStyle.width || "60px",
            height: cardStyle?.iconStyle.height || "60px",
            objectFit: "cover",
          }}
        />
      ) : (
        <div className="w-full h-full bg-gray-100"></div>
      )}
      {cardStyle?.visibleLabel && (
        <span
          style={{
            color: cardStyle.labelStyle?.color || "black",
            fontSize: cardStyle.labelStyle?.fontSize || 12,
          }}
        >
          {title}
        </span>
      )}
    </div>
  );
};
