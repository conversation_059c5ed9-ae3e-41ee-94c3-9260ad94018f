import React, { useState } from "react";
import { Draggable } from "react-beautiful-dnd";
import { cn } from "@/lib/utils";
import { <PERSON><PERSON><PERSON>, CopyPlus, Trash2 } from "lucide-react";

interface DraggableSectionProps {
  id: string;
  index: number;
  children: React.ReactNode;
  className?: string;
  dragBackgroundColor?: string;
  onEdit?: (id: string) => void;
  onCopy?: (id: string) => void;
  onDelete?: (id: string) => void;
}

export const DraggableSection: React.FC<DraggableSectionProps> = ({
  id,
  index,
  children,
  className,
  dragBackgroundColor,
  onEdit,
  onCopy,
  onDelete,
}) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <Draggable draggableId={id} index={index}>
      {(provided, snapshot) => (
        <div
          ref={provided.innerRef}
          {...provided.draggableProps}
          className={cn(
            "relative transition-transform duration-200 group",
            snapshot.isDragging && "scale-102 shadow-lg rotate-1",
            className
          )}
          style={{
            backgroundColor: snapshot.isDragging
              ? dragBackgroundColor
              : undefined,
            ...provided.draggableProps.style,
          }}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          {/* Drag Handle */}
          <div {...provided.dragHandleProps}>{children}</div>

          {/* Hover Menu */}
          {isHovered && !snapshot.isDragging && (
            <div className="absolute top-0 right-0 flex flex-col bg-white border border-gray-200 rounded-xs shadow-lg z-50 overflow-hidden">
              {onEdit && (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onEdit(id);
                  }}
                  className="p-2 hover:bg-gray-100 transition-colors duration-150 flex items-center justify-center"
                  title="Edit"
                >
                  <Pencil size={16} className="text-gray-600" />
                </button>
              )}

              {onCopy && (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onCopy(id);
                  }}
                  className="p-2 hover:bg-gray-100 transition-colors duration-150 flex items-center justify-center"
                  title="Copy"
                >
                  <CopyPlus size={16} className="text-gray-600" />
                </button>
              )}

              {onDelete && (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onDelete(id);
                  }}
                  className="p-2 hover:bg-red-50 transition-colors duration-150 flex items-center justify-center"
                  title="Delete"
                >
                  <Trash2 size={16} className="text-red-600" />
                </button>
              )}
            </div>
          )}
        </div>
      )}
    </Draggable>
  );
};
