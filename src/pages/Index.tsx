import { useState } from "react";
import MobileApp from "./MobileApp";
import { Button } from "@/components/ui/button";

const Index = () => {
  const [currentView, setCurrentView] = useState<"original" | "demo">("demo");

  return (
    <div className="min-h-screen bg-background">
      {/* Content */}
      <div className="flex items-center justify-center p-8">
        <div className="text-center space-y-8">
          <MobileApp />
        </div>
      </div>
    </div>
  );
};

export default Index;
